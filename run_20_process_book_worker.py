# run_20_process_book_worker.py

import logging
import signal
import sys
import time
from pathlib import Path

import redis
from app import settings

# Удален импорт add_to_processed_cache - теперь кэш обновляется атомарно в queue_manager
from app.processing.book_processor import BookProcessor
from app.processing.error_handler import (
    Error<PERSON><PERSON><PERSON>,
    ProcessingError,
    QuarantineError,
    QuarantineType,
)
from app.processing.file_manager import FileManager
from app.processing.queue_manager import TaskQueueManager
from app.storage.local import LocalStorageManager


class BookWorker:
    """Легковесный воркер для обработки книг из очереди Redis.

    Ответственности (Single Responsibility Principle):
    - Управление задачами в очереди (захват/завершение)
    - Файловые операции (перемещение файлов между директориями)
    - Обработка ошибок и retry логика
    - Координация между очередью и бизнес-логикой

    Вся бизнес-логика обработки книг инкапсулирована в BookProcessor.
    Это обеспечивает четкое разделение ответственностей и упрощает тестирование.
    """

    def __init__(self, debug: bool = False):
        """Конструктор.

        Args:
            debug: Включить подробный вывод в stdout. При `False` консоль выводит
                   лишь 1-2 строки на книгу (Ok / Quarantine / Error). Полные
                   логи всегда пишутся в `worker.log`.
        """

        # Настраиваем корневой логгер в зависимости от режима.
        self.setup_logging(debug=debug)

        # Логгер текущего модуля (используется всюду в BookWorker).
        self.logger = logging.getLogger(__name__)

        # Основные компоненты воркера
        self.queue_manager = TaskQueueManager()
        self.file_manager = FileManager(enable_quarantine_processing=settings.QUARANTINE_PROCESSING_ENABLED)
        self.storage_manager = LocalStorageManager()
        self.book_processor = BookProcessor(storage_manager=self.storage_manager)  # Передаём StorageManager внутрь BookProcessor
        self.error_handler = ErrorHandler()

        # Redis соединение для обновления кэша обработанных файлов
        self.redis_client = redis.from_url(settings.REDIS_URL)

        # Флаг для graceful shutdown
        self.should_stop = False
        self.current_task = None

        # ОПТИМИЗАЦИЯ: Предзагрузка и кэширование путей источников
        self._build_source_paths_cache()

        # Регистрируем обработчики сигналов
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("📚 Воркер с модульной архитектурой готов к работе")
        self.logger.info("🏗️ Бизнес-логика инкапсулирована в BookProcessor")

    def setup_logging(self, debug: bool = False):
        """Гибкая настройка логирования.

        • В *debug*-режиме:
            – Корневой логгер DEBUG → печатает все сообщения в консоль.
            – Формат: с метаданными (timestamp, модуль, уровень).

        • В обычном режиме:
            – Корневой логгер ERROR → подавляет шум от сторонних модулей.
            – В stdout выводятся **только** сообщения самого BookWorker
              уровней INFO / WARNING / ERROR (одна-две строки на книгу).
            – Подробный лог любых уровней всегда идёт в `worker.log`.
        """

        # --------------------------------------------------
        # 1. Общий файл-логгер (всегда полный вывод)
        # --------------------------------------------------
        file_handler = logging.FileHandler("worker.log", encoding="utf-8")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))

        # --------------------------------------------------
        # 2. Настройка root-логгера
        # --------------------------------------------------
        # Всегда храним полный DEBUG-лог в файле. Корневой логгер поэтому
        # остаётся на уровне DEBUG; разницы между debug/production для файла нет.
        logging.basicConfig(level=logging.DEBUG, handlers=[file_handler])

        # --------------------------------------------------
        # 3. Отдельный StreamHandler только для BookWorker
        # --------------------------------------------------
        if not debug:
            # Минимальный формат – лишь текст сообщения.
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setLevel(logging.INFO)
            stream_handler.setFormatter(logging.Formatter("%(message)s"))

            worker_logger = logging.getLogger(__name__)
            worker_logger.setLevel(logging.INFO)
            worker_logger.addHandler(stream_handler)
            # Чтобы сообщение не доходило до root (иначе оно будет отброшено
            # уровнем ERROR) и не дублировалось в stdout.
            worker_logger.propagate = False
        else:
            # В debug-режиме достаточно root-StreamHandler-а.
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setLevel(logging.DEBUG)
            stream_handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
            logging.getLogger().addHandler(stream_handler)

    def _build_source_paths_cache(self):
        """ОПТИМИЗАЦИЯ: Строит кэш путей источников для O(1) поиска без дисковых операций.

        Реализует паттерн из документации doc/operations/pipeline01.md:
        - Единовременное построение при старте воркера
        - Словарь {source_type: source_dir} для мгновенного поиска
        - Избегает повторных импортов и итераций settings.SOURCE_DIRS
        """
        from app import settings

        self._source_paths_cache = {}

        for dir_path in settings.SOURCE_DIRS:
            if dir_path.is_dir():
                source_name = dir_path.name
                source_type = settings.SOURCE_TYPE_MAP.get(source_name)
                if source_type:
                    self._source_paths_cache[source_type] = dir_path

        self.logger.info(f"🚀 Кэш путей источников построен: {len(self._source_paths_cache)} директорий")

    def _get_source_dir(self, task_data: dict) -> Path:
        """Получает исходную директорию для source_type из кэша.

        Args:
            task_data: Данные задачи с source_type

        Returns:
            Путь к исходной директории

        Raises:
            ProcessingError: Если source_type неизвестен

        """
        source_type = task_data.get("source_type")

        if not source_type:
            raise ProcessingError(f"Задача должна содержать 'source_type'. Получено: {task_data}")

        source_dir = self._source_paths_cache.get(source_type)

        if source_dir is None:
            raise ProcessingError(f"Не найдена директория источника для source_type={source_type}")

        return source_dir

    def _detect_task_format(self, task_data: dict) -> tuple[str, str]:
        """Определяет формат задачи и извлекает название книги.

        Args:
            task_data: Данные задачи

        Returns:
            Кортеж (format, book_title) где format - "old" или "new"
        """
        # Новый формат содержит archive_path и book_filename
        if "archive_path" in task_data and "book_filename" in task_data:
            book_title = task_data.get("book_filename", "unknown")
            return "new", book_title

        # Старый формат содержит filename
        elif "filename" in task_data:
            book_title = task_data.get("filename", "unknown")
            return "old", book_title

        else:
            raise ProcessingError(
                f"Неопознанный формат задачи. Ожидается 'filename' или 'archive_path' + 'book_filename'. Получено: {task_data}"
            )

    def _handle_old_task_format(self, task_data: dict) -> tuple[Path, Path]:
        """Обрабатывает задачу в старом формате {"filename": "book.zip"}.

        Args:
            task_data: Данные задачи в старом формате

        Returns:
            Кортеж (source_dir, in_progress_path)
        """
        filename = task_data["filename"]
        source_dir = self._get_source_dir(task_data)

        # Перемещаем файл в in_progress
        source_path = source_dir / filename
        in_progress_path = self.file_manager.move_to_in_progress(source_path, source_dir)

        return source_dir, in_progress_path

    def _handle_new_task_format(self, task_data: dict) -> tuple[Path, Path]:
        """Обрабатывает задачу в новом формате с archive_path и book_filename.

        Args:
            task_data: Данные задачи в новом формате

        Returns:
            Кортеж (source_dir, in_progress_path)
        """
        archive_path = task_data["archive_path"]
        source_dir = self._get_source_dir(task_data)

        # Полный путь к архиву от корня источника
        source_path = source_dir / archive_path

        if not source_path.exists():
            raise ProcessingError(f"Архив не найден: {source_path}")

        # Перемещаем архив в in_progress
        in_progress_path = self.file_manager.move_to_in_progress(source_path, source_dir)

        return source_dir, in_progress_path

    def _get_display_name(self, task_data: dict, task_format: str, in_progress_path: Path) -> str:
        """Формирует понятное имя для отображения в логах.

        Args:
            task_data: Данные задачи
            task_format: Формат задачи ("old" или "new")
            in_progress_path: Путь к файлу в обработке

        Returns:
            Отображаемое имя файла
        """
        if task_format == "new":
            # Для нового формата показываем book_filename
            return task_data.get("book_filename", in_progress_path.name)
        else:
            # Для старого формата используем filename или имя файла
            return task_data.get("filename", in_progress_path.name)

    def run(self):
        """Основной цикл воркера"""
        self.logger.info("🚀 Запуск воркера обработки книг")

        processed_count = 0
        start_time = time.time()

        while not self.should_stop:
            try:
                # Захватываем задачу из очереди
                task_data = self.queue_manager.claim_task(timeout=10)

                if task_data is None:
                    # Очередь пуста, короткая пауза
                    time.sleep(1)
                    continue

                self.current_task = task_data

                # Обрабатываем задачу
                success = self._process_task(task_data)

                if success:
                    processed_count += 1
                    self.logger.info(f"📚 Обработано книг: {processed_count}")

                self.current_task = None

                # Выводим статистику каждые 10 книг
                if processed_count > 0 and processed_count % 10 == 0:
                    self._log_statistics(processed_count, start_time)

            except KeyboardInterrupt:
                self.logger.info("⏸️ Получен сигнал остановки")
                break
            except Exception as e:
                self.logger.error(f"❌ Критическая ошибка в основном цикле: {e}", exc_info=True)
                time.sleep(5)  # Пауза перед продолжением

        self.logger.info(f"🏁 Воркер остановлен. Всего обработано: {processed_count} книг")

    def _process_task(self, task_data: dict) -> bool:
        """Обрабатывает одну задачу из очереди через BookProcessor.

        Поддерживает две структуры задач:
        1. СТАРАЯ: {"filename": "book.zip", "source_type": 1, "source_id": 123}
        2. НОВАЯ: {"archive_path": "/path/to/archive.zip", "book_filename": "123.fb2",
                   "source_type": 1, "source_id": 123, "archive_mtime": 1234567890}

        Воркер отвечает только за:
        - Управление файловыми операциями (захват/возврат файлов)
        - Обработку ошибок и завершение задач
        - Координацию между очередью и процессором

        Returns:
            True если задача успешно обработана
        """
        in_progress_path = None
        temp_dir = None
        book_title = "unknown"
        source_dir = None
        task_format = None

        try:
            # 0. Определяем формат задачи и получаем метаданные
            task_format, book_title = self._detect_task_format(task_data)

            # 1. Захватываем файл для обработки (в зависимости от формата)
            if task_format == "old":
                source_dir, in_progress_path = self._handle_old_task_format(task_data)
            else:  # new format
                source_dir, in_progress_path = self._handle_new_task_format(task_data)

            # 2. Основная обработка
            result = self.book_processor.process(task_data)
            book_title = result.get("title", book_title)

            # 3. Завершение задачи
            temp_dir = None  # В новой архитектуре temp_dir не используется
            skipped = result.get("status") == "skipped"
            self._complete_task_success(task_data, in_progress_path, source_dir, temp_dir, skipped)

            # Формируем понятное имя для логов
            display_name = self._get_display_name(task_data, task_format, in_progress_path)
            self.logger.info(f"{display_name} Ok: {book_title}")

            return True

        except Exception as e:
            # Централизованная обработка ошибок
            self._handle_task_error(e, book_title, task_data, in_progress_path, source_dir, temp_dir)
            return False

    def _handle_task_error(
        self,
        error: Exception,
        book_title: str,
        task_data: dict,
        in_progress_path: Path | None = None,
        source_dir: Path | None = None,
        temp_dir: Path | None = None,
    ) -> bool:
        """Централизованный обработчик ошибок для _process_task."""
        # Получаем решение от ErrorHandler для определения типа карантина
        error_decision = self.error_handler.handle_error(error, task_data, "book_processing")

        # Логируем ошибку в зависимости от ее типа
        if isinstance(error, QuarantineError):
            display_name = (
                task_data.get("file_path")
                or task_data.get("filename")
                or (in_progress_path.name if in_progress_path else "unknown")
            )
            self.logger.warning(f"{display_name} Quarantine: {error.message}")
        elif isinstance(error, ProcessingError):
            self.logger.error(f"{task_data.get('filename', 'unknown')} Error: {error.message}")
        else:
            self.logger.error(f"{task_data.get('filename', 'unknown')} Error: {error}", exc_info=True)

        # Очищаем временную директорию, если она есть
        if temp_dir:
            self.file_manager.cleanup_temp_files(temp_dir)

        # Перемещаем файл в карантин, если он был захвачен
        if in_progress_path and source_dir:
            # Используем quarantine_type из решения ErrorHandler
            quarantine_type = error_decision.get("quarantine_type", QuarantineType.ERROR)
            self.file_manager.move_to_quarantine(
                in_progress_path,
                source_dir,
                quarantine_type,
                str(error),
                task_data.get("claimed_at"),
            )

        # Завершаем задачу в Redis как неуспешную
        self.queue_manager.finalize_task(task_data, reason=str(error))
        return False

    def _complete_task_success(
        self,
        task_data: dict,
        in_progress_path: Path,
        source_dir: Path,
        temp_dir: Path | None,
        skipped: bool = False,
    ):
        """Завершает успешно обработанную задачу."""
        reason = "skipped_duplicate" if skipped else "processed"

        # Перемещаем файл в /processed/
        if not skipped and "source_id" in task_data:
            self.file_manager.move_to_processed(in_progress_path, source_dir, task_data["source_id"])
        elif skipped:
            # Если дубликат, просто удаляем из in_progress
            in_progress_path.unlink()
            self.logger.info(f"Skipped: {in_progress_path.name} (дубликат)")

        # Очищаем временную директорию
        if temp_dir:
            self.file_manager.cleanup_temp_files(temp_dir)

        # Завершаем задачу в Redis
        self.queue_manager.finalize_task(task_data, reason=reason)

    def _log_statistics(self, processed_count: int, start_time: float):
        """Выводит статистику работы"""
        elapsed = time.time() - start_time
        rate = processed_count / elapsed if elapsed > 0 else 0

        queue_stats = self.queue_manager.get_queue_stats()
        retry_stats = self.error_handler.get_retry_stats()

        self.logger.info(
            f"""
📊 СТАТИСТИКА ВОРКЕРА:
- Обработано книг: {processed_count}
- Скорость: {rate:.2f} книг/сек
- Время работы: {elapsed:.0f} сек
- Очередь новых: {queue_stats["new_tasks"]}
- В обработке: {queue_stats["processing_tasks"]}
- Завершено успешно: {queue_stats["completed_tasks"]}
- Кэш обработанных: {queue_stats["cached_processed"]}
- Файлов с повторами: {retry_stats["files_with_retries"]}
        """
        )

    def _signal_handler(self, signum, frame):
        """Обработчик сигналов для graceful shutdown"""
        self.logger.info(f"📡 Получен сигнал {signum}. Начинаем graceful shutdown...")
        self.should_stop = True

        # Если есть текущая задача, даем ей время завершиться
        if self.current_task:
            self.logger.info("⏳ Ожидаем завершения текущей задачи...")
            # В реальной реализации можно добавить таймаут


def main():
    """Точка входа в приложение"""
    import argparse

    parser = argparse.ArgumentParser(description="Воркер для обработки книг.")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Включить подробное логирование для отладки.",
    )
    args = parser.parse_args()

    # Создаём воркер с нужным уровнем логирования.
    worker = BookWorker(debug=args.debug)
    worker.run()


if __name__ == "__main__":
    main()
